from maix import image, camera, display, app, time
import cv2
import numpy as np

# 常量定义
CIRCLE_DASH_ANGLE_STEP = 30  # 圆形虚线绘制的角度步长
SHAPE_DASH_ANGLE_STEP = 45   # 形状预测指示器的角度步长
DASH_LINE_SEGMENTS = 10      # 虚线分段数
PREDICTION_INDICATOR_RADIUS = 10  # 预测指示器半径

# ==================== 卡尔曼滤波器类 ====================
class KalmanFilter:
    def __init__(self, process_noise=0.03, measurement_noise=0.1):
        self.kalman = cv2.KalmanFilter(4, 2)  # 状态变量: [x, y, dx, dy], 测量变量: [x, y]
        self.kalman.measurementMatrix = np.array([[1, 0, 0, 0], [0, 1, 0, 0]], np.float32)
        self.kalman.transitionMatrix = np.array([[1, 0, 1, 0], [0, 1, 0, 1], [0, 0, 1, 0], [0, 0, 0, 1]], np.float32)
        self.kalman.processNoiseCov = np.array([[1, 0, 0, 0], [0, 1, 0, 0], [0, 0, 1, 0], [0, 0, 0, 1]], np.float32) * process_noise
        self.kalman.measurementNoiseCov = np.array([[1, 0], [0, 1]], np.float32) * measurement_noise
        self.initialized = False
        
    def init(self, x, y):
        self.kalman.statePre = np.array([[x], [y], [0], [0]], np.float32)
        self.kalman.statePost = np.array([[x], [y], [0], [0]], np.float32)
        self.initialized = True
        
    def predict(self):
        if not self.initialized:
            return None
        predicted = self.kalman.predict()
        return (int(predicted[0]), int(predicted[1]))
        
    def correct(self, x, y):
        measurement = np.array([[x], [y]], np.float32)
        corrected = self.kalman.correct(measurement)
        return (int(corrected[0]), int(corrected[1]))

# 顶点跟踪的卡尔曼滤波器类
class VertexKalmanFilter:
    def __init__(self, num_vertices, process_noise=0.03, measurement_noise=0.1):
        self.vertex_filters = [KalmanFilter(process_noise, measurement_noise) for _ in range(num_vertices)]
        self.num_vertices = num_vertices
        self.initialized = False
        
    def init(self, vertices):
        if len(vertices) != self.num_vertices:
            print(f"错误: 顶点数量不匹配，期望 {self.num_vertices}，实际 {len(vertices)}")
            self.initialized = False
            return
        
        try:
            for i, vertex in enumerate(vertices):
                self.vertex_filters[i].init(vertex[0], vertex[1])
            self.initialized = True
        except Exception as e:
            print(f"顶点滤波器初始化错误: {e}")
            self.initialized = False
        
    def predict(self):
        if not self.initialized:
            return None
        
        try:
            predicted_vertices = []
            for filter in self.vertex_filters:
                pred = filter.predict()
                if pred:
                    predicted_vertices.append(pred)
                else:
                    return None
            
            return predicted_vertices
        except Exception as e:
            print(f"顶点滤波器预测错误: {e}")
            return None
        
    def correct(self, vertices):
        if len(vertices) != self.num_vertices:
            print(f"错误: 顶点数量不匹配，期望 {self.num_vertices}，实际 {len(vertices)}")
            return vertices
        
        corrected_vertices = []
        for i, vertex in enumerate(vertices):
            corrected = self.vertex_filters[i].correct(vertex[0], vertex[1])
            corrected_vertices.append(corrected)
        
        return corrected_vertices

# ==================== 初始化相机和显示 ====================
cam = camera.Camera(640, 480, image.Format.FMT_BGR888)
disp = display.Display()

# ==================== 辅助函数 ====================
def get_value_from_history(values, use_latest=True):
    """从历史记录中获取值，默认使用最新值"""
    if not values:
        return 0

    # 返回最新的值
    if use_latest or len(values) < 2:
        return values[-1]

    # 如果需要平滑值（备用），返回最近几个值的简单平均
    return int(sum(values[-3:]) / min(len(values), 3))

def calculate_area_ratio(area1, area2):
    """计算两个面积的比例，确保结果在0-1之间"""
    max_area = max(area1, area2)
    if max_area == 0:
        return 1.0  # 如果两个面积都为0，认为完全匹配
    return min(area1, area2) / max_area

def calculate_distance(point1, point2):
    """计算两点之间的欧几里得距离"""
    return np.sqrt((point1[0] - point2[0])**2 + (point1[1] - point2[1])**2)

def is_duplicate_shape(cx, cy, area, detected_shapes, shape_vertices, duplicate_distance_threshold, duplicate_area_ratio):
    """检查是否是重复的形状"""
    for detected_cx, detected_cy, detected_area, detected_vertices in detected_shapes:
        # 如果类型相同
        if detected_vertices == shape_vertices:
            # 检查中心点距离和面积比例
            distance = calculate_distance((cx, cy), (detected_cx, detected_cy))
            area_ratio = calculate_area_ratio(area, detected_area)

            # 如果中心点距离很近且面积比例接近1，认为是重复
            if distance < duplicate_distance_threshold and area_ratio > duplicate_area_ratio:
                return True
    return False

def find_matching_shape_position(cx, cy, shape_type, shape_tracking_data, position_tolerance):
    """查找匹配的形状位置"""
    shape_position = None
    min_distance = float('inf')

    for pos in shape_tracking_data.keys():
        if pos[0] == shape_type:
            distance = calculate_distance((cx, cy), (pos[1], pos[2]))
            if distance < position_tolerance and distance < min_distance:
                min_distance = distance
                shape_position = pos

    return shape_position

def count_vertices_inside_circle(quad_approx, circle_cx, circle_cy, circle_radius):
    """计算四边形有多少个顶点在圆内"""
    vertices_inside = 0
    for vertex in quad_approx:
        # vertex是一个包含[x, y]的数组
        vertex_x, vertex_y = vertex[0][0], vertex[0][1]
        vertex_distance = calculate_distance((vertex_x, vertex_y), (circle_cx, circle_cy))
        if vertex_distance < circle_radius:
            vertices_inside += 1
    return vertices_inside

def calculate_overlap_area(quad_approx, circle_cx, circle_cy, circle_radius):
    """
    计算四边形与圆形的重叠面积比例
    返回重叠面积占四边形面积的比例 (0.0 - 1.0)
    """
    try:
        # 计算四边形面积
        quad_area = cv2.contourArea(quad_approx)
        if quad_area <= 0:
            return 0.0

        # 创建一个足够大的画布来绘制形状
        canvas_size = max(800, int(circle_radius * 3))

        # 计算偏移量，将圆心放在画布中心
        offset_x = canvas_size // 2 - circle_cx
        offset_y = canvas_size // 2 - circle_cy

        # 调整四边形顶点坐标
        adjusted_quad = quad_approx.copy()
        for i in range(len(adjusted_quad)):
            adjusted_quad[i][0][0] += offset_x
            adjusted_quad[i][0][1] += offset_y

        # 调整圆心坐标
        adjusted_circle_cx = canvas_size // 2
        adjusted_circle_cy = canvas_size // 2

        # 绘制四边形
        quad_mask = np.zeros((canvas_size, canvas_size), dtype=np.uint8)
        cv2.fillPoly(quad_mask, [adjusted_quad], 255)

        # 绘制圆形
        circle_mask = np.zeros((canvas_size, canvas_size), dtype=np.uint8)
        cv2.circle(circle_mask, (adjusted_circle_cx, adjusted_circle_cy), circle_radius, 255, -1)

        # 计算重叠区域
        overlap_mask = cv2.bitwise_and(quad_mask, circle_mask)
        overlap_area = cv2.countNonZero(overlap_mask)

        # 计算重叠比例
        overlap_ratio = overlap_area / cv2.countNonZero(quad_mask) if cv2.countNonZero(quad_mask) > 0 else 0.0

        return min(overlap_ratio, 1.0)  # 确保不超过1.0

    except Exception as e:
        print(f"计算重叠面积时出错: {e}")
        return 0.0

def is_quad_inside_circle_hybrid(quad_approx, circle_cx, circle_cy, circle_radius,
                                min_vertices_conservative=2, min_vertices_aggressive=3):
    """
    混合策略判断四边形是否在圆内
    结合中心点检查和顶点检查

    参数:
    - min_vertices_conservative: 保守过滤时，中心在圆内需要的最少顶点数
    - min_vertices_aggressive: 积极过滤时，中心在圆内需要的最少顶点数
    """
    # 计算四边形中心点
    M = cv2.moments(quad_approx)
    if M["m00"] == 0:
        return False, "无法计算中心点"

    quad_cx = int(M["m10"] / M["m00"])
    quad_cy = int(M["m01"] / M["m00"])

    # 策略1: 检查中心点是否在圆内
    center_distance = calculate_distance((quad_cx, quad_cy), (circle_cx, circle_cy))
    center_inside = center_distance < circle_radius

    # 策略2: 检查顶点在圆内的数量
    vertices_inside_count = count_vertices_inside_circle(quad_approx, circle_cx, circle_cy, circle_radius)

    # 混合判断逻辑
    if center_inside and vertices_inside_count >= min_vertices_aggressive:
        # 中心在圆内且满足积极过滤条件 -> 确定在圆内
        reason = f"中心在圆内(距离:{center_distance:.1f}<{circle_radius}) 且 {vertices_inside_count}/4 个顶点在圆内 (积极过滤)"
        return True, reason
    elif center_inside and vertices_inside_count >= min_vertices_conservative:
        # 中心在圆内且满足保守过滤条件 -> 可能在圆内
        reason = f"中心在圆内(距离:{center_distance:.1f}<{circle_radius}) 且 {vertices_inside_count}/4 个顶点在圆内 (保守过滤)"
        return True, reason
    elif vertices_inside_count == 4:
        # 所有顶点都在圆内 -> 确定在圆内（即使中心可能因为计算误差不在圆内）
        reason = f"所有4个顶点都在圆内 (中心距离:{center_distance:.1f})"
        return True, reason
    else:
        # 其他情况 -> 不在圆内
        reason = f"中心{'在圆内' if center_inside else '在圆外'}(距离:{center_distance:.1f}) 且仅 {vertices_inside_count}/4 个顶点在圆内"
        return False, reason

def is_quad_inside_circle_area_overlap(quad_approx, circle_cx, circle_cy, circle_radius, overlap_threshold=0.7):
    """
    基于面积重叠判断四边形是否在圆内

    参数:
    - overlap_threshold: 重叠面积比例阈值，超过此值认为四边形在圆内
    """
    overlap_ratio = calculate_overlap_area(quad_approx, circle_cx, circle_cy, circle_radius)

    if overlap_ratio >= overlap_threshold:
        reason = f"面积重叠比例: {overlap_ratio:.2f} >= {overlap_threshold} (面积重叠过滤)"
        return True, reason
    else:
        reason = f"面积重叠比例: {overlap_ratio:.2f} < {overlap_threshold}"
        return False, reason

def is_quad_inside_circle_comprehensive(quad_approx, circle_cx, circle_cy, circle_radius,
                                      min_vertices_conservative=2, min_vertices_aggressive=3,
                                      overlap_threshold=0.7):
    """
    综合策略判断四边形是否在圆内
    结合混合策略（中心点+顶点）和面积重叠检查

    优先级：
    1. 面积重叠检查（处理顶点都在圆外但面积高度重合的情况）
    2. 混合策略检查（中心点+顶点检查）
    """
    # 策略1: 面积重叠检查（优先级最高）
    area_inside, area_reason = is_quad_inside_circle_area_overlap(
        quad_approx, circle_cx, circle_cy, circle_radius, overlap_threshold
    )
    if area_inside:
        return True, area_reason

    # 策略2: 混合策略检查（中心点+顶点）
    hybrid_inside, hybrid_reason = is_quad_inside_circle_hybrid(
        quad_approx, circle_cx, circle_cy, circle_radius,
        min_vertices_conservative, min_vertices_aggressive
    )
    if hybrid_inside:
        return True, hybrid_reason

    # 都不满足条件，不在圆内
    return False, f"综合判断：{area_reason} 且 {hybrid_reason}"

def process_retained_quadrilateral(quad_approx, cx, cy, area, is_max_rect, frame_count,
                                 shape_tracking_data, kalman_filters, vertex_kalman_filters,
                                 vertex_history, use_kalman, process_noise, measurement_noise,
                                 vertex_history_size, edge_calc_method, position_tolerance,
                                 last_frame_shapes, img_result, show_area, edge_history,
                                 edge_history_size, use_instant_values):
    """
    处理保留的四边形（未被过滤的四边形）
    进行完整的跟踪、绘制和计算
    """
    shape = "Quad"  # 四边形
    # 对最大的两个矩形使用特殊颜色
    color = image.COLOR_YELLOW if is_max_rect else image.COLOR_GREEN

    # 提取顶点坐标列表
    vertices = [tuple(pt[0]) for pt in quad_approx]

    # 查找匹配的形状位置并更新跟踪数据
    shape_position = find_matching_shape_position(cx, cy, "Quad", shape_tracking_data, position_tolerance)
    shape_position, cx, cy, vertices = update_shape_tracking(
        shape_position, cx, cy, vertices, frame_count, shape_tracking_data,
        kalman_filters, vertex_kalman_filters, vertex_history,
        use_kalman, process_noise, measurement_noise,
        vertex_history_size, edge_calc_method, "Quad"
    )

    # 记录该四边形到当前帧四边形列表
    if "Quad" not in last_frame_shapes:
        last_frame_shapes["Quad"] = []
    last_frame_shapes["Quad"].append((cx, cy, area))

    # 在控制台打印形状信息
    print(f"\n检测到新的{shape}，面积: {int(area)}，拐点数量: 4")

    # 在图像上标记识别结果
    img_result.draw_string(cx-20, cy, shape, color)
    # 如果启用，显示轮廓面积
    if show_area:
        area_text = f"A:{int(area)}"
        img_result.draw_string(cx-20, cy+15, area_text, color)

    # 画出轮廓和拐点，并显示边长
    draw_shape_edges_and_calculate_lengths(img_result, vertices, shape_position, shape, color,
                                          edge_history, edge_history_size, use_instant_values)

    return shape_position, cx, cy, vertices

def update_shape_tracking(shape_position, cx, cy, vertices, frame_count, shape_tracking_data,
                         kalman_filters, vertex_kalman_filters, vertex_history,
                         use_kalman, process_noise, measurement_noise,
                         vertex_history_size, edge_calc_method, shape_type):
    """更新形状跟踪数据"""
    if shape_position is None:
        # 创建新的位置标识
        shape_position = (shape_type, cx, cy)
        shape_tracking_data[shape_position] = frame_count

        # 为新形状创建卡尔曼滤波器
        if use_kalman:
            kalman_filters[shape_position] = KalmanFilter(process_noise, measurement_noise)
            kalman_filters[shape_position].init(cx, cy)

            # 如果有顶点信息，创建顶点卡尔曼滤波器
            if isinstance(vertices, list):
                vertex_filter = VertexKalmanFilter(len(vertices), process_noise, measurement_noise)
                try:
                    vertex_filter.init(vertices)
                    if vertex_filter.initialized:
                        vertex_kalman_filters[shape_position] = vertex_filter
                except Exception as e:
                    print(f"顶点滤波器初始化失败: {e}")

        # 初始化顶点历史记录
        if isinstance(vertices, list):
            vertex_history[shape_position] = [vertices]
    else:
        # 更新现有形状
        shape_tracking_data[shape_position] = frame_count

        # 更新卡尔曼滤波器
        if use_kalman:
            # 更新中心点滤波器
            if shape_position in kalman_filters:
                filtered_pos = kalman_filters[shape_position].correct(cx, cy)
                cx, cy = filtered_pos

            # 更新顶点滤波器
            if isinstance(vertices, list) and shape_position in vertex_kalman_filters:
                try:
                    filtered_vertices = vertex_kalman_filters[shape_position].correct(vertices)
                    if edge_calc_method == "filtered":
                        vertices = filtered_vertices
                except Exception as e:
                    print(f"顶点滤波器更新失败: {e}")

        # 更新顶点历史记录
        if isinstance(vertices, list):
            if shape_position in vertex_history:
                vertex_history[shape_position].append(vertices)
                if len(vertex_history[shape_position]) > vertex_history_size:
                    vertex_history[shape_position].pop(0)
            else:
                vertex_history[shape_position] = [vertices]

    return shape_position, cx, cy, vertices

def draw_shape_edges_and_calculate_lengths(img_result, vertices, shape_position, shape, color,
                                         edge_history, edge_history_size, use_instant_values):
    """绘制形状边缘并计算显示边长"""
    for i in range(len(vertices)):
        pt1 = vertices[i]
        pt2 = vertices[(i+1) % len(vertices)]  # 连接到下一个点，形成闭环

        # 绘制线段（边）
        img_result.draw_line(pt1[0], pt1[1], pt2[0], pt2[1], color, 2)

        # 绘制拐点
        img_result.draw_circle(pt1[0], pt1[1], 3, color, thickness=-1)  # 实心圆

        # 计算边长（两点之间的欧几里得距离）
        edge_length = int(calculate_distance(pt1, pt2))

        # 使用形状位置作为键，确保同一位置的形状共享历史数据
        edge_key = (shape_position, i)
        if edge_key not in edge_history:
            edge_history[edge_key] = []

        # 添加当前边长到历史记录
        edge_history[edge_key].append(edge_length)

        # 保持历史记录在指定大小内
        if len(edge_history[edge_key]) > edge_history_size:
            edge_history[edge_key].pop(0)

        # 获取要显示的边长值（当前值或历史平均值）
        display_length = get_value_from_history(edge_history[edge_key], use_instant_values)

        # 计算边的中点坐标，用于显示边长
        mid_x = (pt1[0] + pt2[0]) // 2
        mid_y = (pt1[1] + pt2[1]) // 2

        # 在边的中点显示边长
        length_text = f"{display_length}"
        img_result.draw_string(mid_x, mid_y, length_text, image.COLOR_RED)

        # 在控制台打印边长信息
        edge_info = f"{shape} Edge {i+1}: 当前={edge_length} pixels (from point {i} to point {(i+1) % len(vertices)})"
        print(edge_info)

# 设置参数 - 这些参数可能需要根据实际环境调整
binary_threshold = 120    # 二值化阈值
canny_low = 30           # Canny边缘检测低阈值
canny_high = 90         # Canny边缘检测高阈值
min_area = 100           # 最小轮廓面积，识别更小的轮廓
epsilon_factor = 0.1    # 轮廓近似精度因子

# 闭运算参数
enable_closing = False    # 是否启用闭运算
kernel_size = 3          # 闭运算的核大小，越大效果越明显

# 重复检测参数
duplicate_distance_threshold = 15  # 中心点距离阈值，小于此值认为可能是重复
duplicate_area_ratio = 0.8        # 面积比例阈值，如果面积比例在这个范围内，认为可能是重复

# 圆内四边形过滤参数（混合策略）
min_vertices_for_conservative_filter = 2  # 保守过滤：中心在圆内时，至少需要多少个顶点在圆内
min_vertices_for_aggressive_filter = 3   # 积极过滤：中心在圆内时，至少需要多少个顶点在圆内
area_overlap_threshold = 0.7             # 面积重叠阈值：重叠比例超过此值认为四边形在圆内

# 边长数据参数
edge_history_size = 3   # 保存少量历史数据以备不时之需
edge_history = {}       # 存储边长历史数据的字典 {(shape_type, shape_position, edge_idx): [lengths...]}
circle_radius_history = {}  # 存储圆半径历史数据的字典 {(cx, cy): [radiuses...]}
# 形状位置跟踪参数
position_tolerance = 20  # 认为是同一个形状的位置容差（像素）
last_frame_shapes = {}   # 上一帧识别到的形状 {shape_type: [(cx, cy, area), ...]}
shape_tracking_data = {} # 形状跟踪数据，记录每个位置的形状历史 {(shape_type, cx, cy): frame_count}
# 卡尔曼滤波器参数
kalman_filters = {}     # 每个形状位置的卡尔曼滤波器 {(shape_type, cx, cy): KalmanFilter}
vertex_kalman_filters = {}  # 每个形状顶点的卡尔曼滤波器 {(shape_type, cx, cy): VertexKalmanFilter}
use_kalman = True       # 是否使用卡尔曼滤波器
process_noise = 0.05    # 增加过程噪声，使其更快适应形状变化
measurement_noise = 0.08  # 减少测量噪声，更信任测量值而非预测值
# 边长计算方法
edge_calc_method = "raw"  # 边长计算方法: "raw"使用原始顶点, "filtered"使用滤波后顶点
# 顶点历史记录，用于跟踪变化
vertex_history = {}      # 顶点历史记录 {(shape_type, cx, cy): [vertices_list]}
vertex_history_size = 3  # 减少顶点历史记录数量，更关注最近的测量值
# 边长计算参数
use_instant_values = True  # 使用当前测量值而非平均值
# 形状超时参数
shape_timeout = 20       # 如果超过20帧未检测到，则清理该形状位置的跟踪数据

# 是否显示中间处理结果
show_debug = True
debug_view = 2  # 0: 原图, 1: 二值化, 2: 边缘, 3: 闭运算后

# 是否显示轮廓面积
show_area = True

# 计数变量，用于定时切换视图
view_switch_count = 0  # 视图切换计数器
frame_count = 0  # 总帧计数，用于预处理和形状跟踪

# 预处理相关参数
enable_preprocess = True  # 是否启用预处理筛选功能
preprocess_start_frame = 5  # 从第几帧开始启用预处理
max_rectangles = []  # 存储最大的两个矩形 [(contour, area, approx), ...]
preprocess_started = False  # 是否已经开始预处理
preprocess_stable_frames = 0  # 跟踪最大矩形保持稳定的帧数
preprocess_stable_threshold = 5  # 需要保持稳定的帧数阈值

while not app.need_exit():
    # 计时开始
    t_start = time.ticks_ms()
    
    # 读取图像
    img_maix = cam.read()
    
    # 将MaixPy图像转换为OpenCV格式
    t = time.ticks_ms()
    img_cv = image.image2cv(img_maix, ensure_bgr=True, copy=False)
    t_convert = time.ticks_ms() - t
    
    # 步骤1：图像二值化
    t = time.ticks_ms()
    gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
    _, binary = cv2.threshold(gray, binary_threshold, 255, cv2.THRESH_BINARY)
    t_binary = time.ticks_ms() - t
    
    # 步骤2：应用闭运算（膨胀后腐蚀）
    t = time.ticks_ms()
    if enable_closing:
        # 创建结构元素（核）
        kernel = np.ones((kernel_size, kernel_size), np.uint8)
        # 应用闭运算：先膨胀后腐蚀，填充小洞和连接相近区域
        binary_closed = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
    else:
        # 不需要copy，直接使用binary
        binary_closed = binary
    t_closing = time.ticks_ms() - t
    
    # 步骤3：边缘提取
    t = time.ticks_ms()
    blurred = cv2.GaussianBlur(binary_closed, (3, 3), 0)
    edges = cv2.Canny(blurred, canny_low, canny_high)
    t_edge = time.ticks_ms() - t
    
    # 步骤4：查找轮廓
    t = time.ticks_ms()
    contours, _ = cv2.findContours(edges, cv2.RETR_TREE, cv2.CHAIN_APPROX_NONE)
    t_contour = time.ticks_ms() - t
    
    # 排序轮廓，从大到小
    contours = sorted(contours, key=cv2.contourArea, reverse=True)
    
    # 预处理步骤：识别最大的两个矩形并创建感兴趣区域
    if enable_preprocess and frame_count >= preprocess_start_frame:
        # 第一次运行预处理时，初始化最大矩形列表
        if not preprocess_started:
            print("开始预处理：识别最大的两个矩形")
            preprocess_started = True
            max_rectangles = []
        
        # 如果还没找到两个最大矩形，则尝试找出
        if len(max_rectangles) < 2:
            rect_found_this_frame = False
            for contour in contours:
                area = cv2.contourArea(contour)
                if area < min_area:
                    continue
                
                # 近似轮廓为多边形
                epsilon = epsilon_factor * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)
                
                # 如果是四边形
                if len(approx) == 4:
                    # 检查是否与已有的矩形重复
                    is_duplicate = False
                    for _, existing_area, existing_approx in max_rectangles:
                        # 计算中心点
                        M1 = cv2.moments(approx)
                        M2 = cv2.moments(existing_approx)
                        if M1["m00"] != 0 and M2["m00"] != 0:
                            cx1 = int(M1["m10"] / M1["m00"])
                            cy1 = int(M1["m01"] / M1["m00"])
                            cx2 = int(M2["m10"] / M2["m00"])
                            cy2 = int(M2["m01"] / M2["m00"])
                            
                            # 计算距离和面积比
                            distance = np.sqrt((cx1-cx2)**2 + (cy1-cy2)**2)
                            area_ratio = min(area, existing_area) / max(area, existing_area)
                            
                            # 判断是否重复
                            if distance < duplicate_distance_threshold and area_ratio > duplicate_area_ratio:
                                is_duplicate = True
                                break
                    
                    # 如果不是重复的且面积够大，加入最大矩形列表
                    if not is_duplicate:
                        max_rectangles.append((contour, area, approx))
                        max_rectangles = sorted(max_rectangles, key=lambda x: x[1], reverse=True)[:2]
                        rect_found_this_frame = True
                        print(f"找到新的大矩形，面积: {area}, 目前已找到 {len(max_rectangles)}/2")
                        
                        # 如果已经找到两个，打印确认信息
                        if len(max_rectangles) == 2:
                            print("已找到最大的两个矩形，开始筛选处理")
                            for i, (_, rect_area, _) in enumerate(max_rectangles):
                                print(f"矩形 {i+1} 面积: {rect_area}")
            
            # 如果本帧没有找到新矩形，重置稳定帧计数
            if not rect_found_this_frame and len(max_rectangles) < 2:
                preprocess_stable_frames = 0
        
        # 如果已经找到两个最大矩形，筛选轮廓
        if len(max_rectangles) == 2:
            # 创建掩码，用于判断点是否在两个大矩形组成的区域内
            mask = np.zeros_like(gray)
            
            # 绘制两个大矩形到掩码上
            try:
                for _, _, approx in max_rectangles:
                    cv2.drawContours(mask, [approx], 0, 255, -1)
                
                # 检查掩码是否有效（至少有一些像素被设置）
                mask_pixels = np.sum(mask > 0)
                if mask_pixels < 100:  # 确保掩码至少有100个像素
                    print(f"警告：生成的掩码像素太少 ({mask_pixels})，可能未正确识别矩形区域")
                    # 在这种情况下，暂时不进行过滤
                    preprocess_stable_frames = 0  # 重置稳定帧计数
                else:
                    # 增加稳定帧计数
                    preprocess_stable_frames += 1
                    print(f"预处理：矩形区域稳定度 {preprocess_stable_frames}/{preprocess_stable_threshold}，掩码像素数: {mask_pixels}，掩码占比: {mask_pixels/(gray.shape[0]*gray.shape[1])*100:.2f}%")
                    
                    # 只有当稳定帧数达到阈值时，才应用掩码过滤
                    if preprocess_stable_frames >= preprocess_stable_threshold:
                        # 筛选轮廓，只保留在掩码内的轮廓
                        filtered_contours = []
                        for contour in contours:
                            # 计算轮廓中心点
                            M = cv2.moments(contour)
                            if M["m00"] != 0:
                                cx = int(M["m10"] / M["m00"])
                                cy = int(M["m01"] / M["m00"])
                                
                                # 检查中心点是否在掩码内
                                if 0 <= cy < mask.shape[0] and 0 <= cx < mask.shape[1] and mask[cy, cx] > 0:
                                    filtered_contours.append(contour)
                            else:
                                # 如果不能计算中心点，我们检查轮廓上的点是否在掩码内
                                inside_points = 0
                                for point in contour:
                                    px, py = point[0]
                                    if 0 <= py < mask.shape[0] and 0 <= px < mask.shape[1] and mask[py, px] > 0:
                                        inside_points += 1
                                # 如果至少有一半的点在掩码内，我们认为这个轮廓在区域内
                                if inside_points >= len(contour) / 2:
                                    filtered_contours.append(contour)
                        
                        # 更新轮廓列表
                        contours = filtered_contours
                        print(f"预处理：保留了 {len(contours)} 个位于主矩形内的轮廓")
                    else:
                        print(f"预处理：等待矩形区域稳定 ({preprocess_stable_frames}/{preprocess_stable_threshold})")
            except Exception as e:
                print(f"掩码创建或应用出错: {e}")
                preprocess_stable_frames = 0  # 出错时重置稳定帧计数
        else:
            # 如果还没有找到两个矩形，重置稳定帧计数
            preprocess_stable_frames = 0
    
    # 选择显示哪个视图
    if show_debug:
        if debug_view == 0:
            img_result = image.cv2image(img_cv, bgr=True, copy=False)
        elif debug_view == 1:
            # 将二值图像转换为可显示的MaixPy图像
            binary_colored = np.stack([binary, binary, binary], axis=2)
            img_result = image.cv2image(binary_colored)
        elif debug_view == 2:
            # 将边缘图像转换为可显示的MaixPy图像
            edges_colored = np.stack([edges, edges, edges], axis=2)
            img_result = image.cv2image(edges_colored)
        else:  # debug_view == 3
            # 将闭运算后的二值图像转换为可显示的MaixPy图像
            closed_colored = np.stack([binary_closed, binary_closed, binary_closed], axis=2)
            img_result = image.cv2image(closed_colored)
    else:
        img_result = image.cv2image(img_cv, bgr=True, copy=False)
    
    # 步骤5：识别形状
    triangle_count = 0
    quadrilateral_count = 0
    circle_count = 0       # 添加圆形计数
    min_detected_area = float('inf')  # 跟踪检测到的最小面积
    
    # 用于跟踪已检测到的形状，防止重复
    detected_shapes = []  # 将存储 (cx, cy, area, vertices)
    
    # 先识别所有四边形
    quadrilaterals = []
    
    for contour in contours:
        # 计算轮廓面积
        area = cv2.contourArea(contour)
        
        # 过滤掉太小的轮廓
        if area < min_area:
            continue
            
        # 近似轮廓为多边形
        epsilon = epsilon_factor * cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, epsilon, True)
        
        # 根据拐点数量识别形状
        num_vertices = len(approx)
        
        # 找出所有四边形并保存
        if num_vertices == 4:
            # 计算四边形的中心点
            M = cv2.moments(contour)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                
                # 检查是否是最大的两个矩形之一
                is_max_rect = False
                if enable_preprocess and preprocess_started and len(max_rectangles) == 2 and preprocess_stable_frames >= preprocess_stable_threshold:
                    for _, max_area, max_approx in max_rectangles:
                        # 计算最大矩形的中心点
                        M_max = cv2.moments(max_approx)
                        if M_max["m00"] != 0:
                            cx_max = int(M_max["m10"] / M_max["m00"])
                            cy_max = int(M_max["m01"] / M_max["m00"])
                            
                            # 计算距离和面积比
                            distance = calculate_distance((cx, cy), (cx_max, cy_max))
                            area_ratio = calculate_area_ratio(area, max_area)
                            
                            # 判断是否是同一个矩形
                            if distance < duplicate_distance_threshold and area_ratio > duplicate_area_ratio:
                                is_max_rect = True
                                break
                
                # 检查是否是重复的四边形
                is_duplicate = is_duplicate_shape(cx, cy, area, detected_shapes, 4,
                                                duplicate_distance_threshold, duplicate_area_ratio)
                
                # 如果不是重复的，则添加到候选四边形列表（延迟详细处理）
                if not is_duplicate:
                    # 存储四边形信息，包含处理所需的所有数据
                    quad_info = {
                        'approx': approx,
                        'cx': cx,
                        'cy': cy,
                        'area': area,
                        'is_max_rect': is_max_rect
                    }
                    quadrilaterals.append(quad_info)
                    detected_shapes.append((cx, cy, area, num_vertices))
                    quadrilateral_count += 1
    
    # 存储检测到的圆形信息，用于后续过滤四边形
    detected_circles = []  # 存储 (cx, cy, radius) 信息

    # 然后处理所有轮廓，识别三角形和圆形
    for contour in contours:
        # 计算轮廓面积
        area = cv2.contourArea(contour)
        
        # 过滤掉太小的轮廓
        if area < min_area:
            continue
            
        # 近似轮廓为多边形
        epsilon = epsilon_factor * cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, epsilon, True)
        
        # 根据拐点数量识别形状
        num_vertices = len(approx)
        
        # 计算周长
        perimeter = cv2.arcLength(contour, True)
        
        # 计算轮廓的中心点
        M = cv2.moments(contour)
        if M["m00"] == 0:
            continue  # 跳过无法计算中心点的轮廓
            
        cx = int(M["m10"] / M["m00"])
        cy = int(M["m01"] / M["m00"])
        
        # 检查这个轮廓是否在任何一个四边形内部
        # 如果已经启用预处理并找到了最大的两个矩形，则预处理已经筛选过轮廓了
        # 此时所有轮廓都在两个最大矩形内，不需要再次检查
        is_inside_quad = True
        if not (enable_preprocess and preprocess_started and len(max_rectangles) == 2 and preprocess_stable_frames >= preprocess_stable_threshold):
            # 如果没有启用预处理或预处理尚未完成，则检查是否在四边形内
            is_inside_quad = False
            for quad in quadrilaterals:
                # 使用OpenCV的pointPolygonTest检查点是否在多边形内
                # 如果返回值大于0，则点在多边形内部
                if cv2.pointPolygonTest(quad, (cx, cy), False) > 0:
                    is_inside_quad = True
                    break
                
        # 只处理在四边形内部的形状
        if not is_inside_quad:
            continue
        
        # 更新检测到的最小面积
        if area < min_detected_area:
            min_detected_area = area
            
        # 识别圆形
        # 圆的特征是：面积与周长的关系接近于 4*π*area = perimeter²
        circularity = 4 * np.pi * area / (perimeter * perimeter) if perimeter > 0 else 0
        
        # 先检查是否是圆形（圆度接近1，表示非常接近圆形）
        if circularity > 0.85:  # 圆度阈值，可以根据实际情况调整
            # 检查是否是重复的圆形
            is_duplicate = is_duplicate_shape(cx, cy, area, detected_shapes, -1,
                                            duplicate_distance_threshold, duplicate_area_ratio)

            # 如果是重复的，跳过
            if is_duplicate:
                continue
            
            shape = "Circle"  # 圆形
            color = image.COLOR_BLUE
            circle_count += 1
            
            # 添加到已检测形状列表，使用-1表示圆形
            detected_shapes.append((cx, cy, area, -1))

            # 计算圆形半径并记录，用于后续过滤四边形
            radius = int(np.sqrt(area / np.pi))
            detected_circles.append((cx, cy, radius))
            
        # 如果为三角形（3个拐点）
        elif num_vertices == 3:
            # 检查是否是重复的三角形
            is_duplicate = is_duplicate_shape(cx, cy, area, detected_shapes, 3,
                                            duplicate_distance_threshold, duplicate_area_ratio)

            # 如果是重复的，跳过
            if is_duplicate:
                continue
            
            shape = "Triangle"  # 三角形
            color = image.COLOR_RED
            triangle_count += 1
            
            # 添加到已检测形状列表
            detected_shapes.append((cx, cy, area, num_vertices))
            

            
        else:
            # 不是三角形也不是圆形，跳过
            continue
        
        # 在控制台打印形状信息
        if shape == "Circle":
            print(f"\n检测到新的{shape}，面积: {int(area)}，圆度: {circularity:.3f}")
        else:
            print(f"\n检测到新的{shape}，面积: {int(area)}，拐点数量: {num_vertices}")
        
        # 在图像上标记识别结果
        img_result.draw_string(cx-20, cy, shape, color)
        # 如果启用，显示轮廓面积
        if show_area:
            area_text = f"A:{int(area)}"
            img_result.draw_string(cx-20, cy+15, area_text, color)
        
        # 如果是三角形，处理和显示边长
        if shape == "Triangle":
            # 提取顶点坐标列表
            vertices = [tuple(pt[0]) for pt in approx]

            # 查找匹配的形状位置并更新跟踪数据
            shape_position = find_matching_shape_position(cx, cy, "Triangle", shape_tracking_data, position_tolerance)
            shape_position, cx, cy, vertices = update_shape_tracking(
                shape_position, cx, cy, vertices, frame_count, shape_tracking_data,
                kalman_filters, vertex_kalman_filters, vertex_history,
                use_kalman, process_noise, measurement_noise,
                vertex_history_size, edge_calc_method, "Triangle"
            )
            
            # 记录该三角形到当前帧三角形列表
            if "Triangle" not in last_frame_shapes:
                last_frame_shapes["Triangle"] = []
            last_frame_shapes["Triangle"].append((cx, cy, area))
            
            # 画出轮廓和拐点，并显示边长
            draw_shape_edges_and_calculate_lengths(img_result, vertices, shape_position, shape, color,
                                                  edge_history, edge_history_size, use_instant_values)
        
        # 如果是圆形，计算和显示半径
        elif shape == "Circle":
            # 查找匹配的形状位置并更新跟踪数据
            shape_position = find_matching_shape_position(cx, cy, "Circle", shape_tracking_data, position_tolerance)
            shape_position, cx, cy, _ = update_shape_tracking(
                shape_position, cx, cy, None, frame_count, shape_tracking_data,
                kalman_filters, vertex_kalman_filters, vertex_history,
                use_kalman, process_noise, measurement_noise,
                vertex_history_size, edge_calc_method, "Circle"
            )
            
            # 记录该圆形到当前帧圆形列表
            if "Circle" not in last_frame_shapes:
                last_frame_shapes["Circle"] = []
            last_frame_shapes["Circle"].append((cx, cy, area))
            
            # 计算等效半径（基于面积的圆半径）
            radius = int(np.sqrt(area / np.pi))
            
            # 使用形状位置作为键，确保同一位置的圆形共享历史数据
            if shape_position not in circle_radius_history:
                circle_radius_history[shape_position] = []
            
            # 添加当前半径到历史记录
            circle_radius_history[shape_position].append(radius)
            
            # 保持历史记录在指定大小内
            if len(circle_radius_history[shape_position]) > edge_history_size:
                circle_radius_history[shape_position].pop(0)
            
            # 使用改进的平均值计算方法计算平均半径
            display_radius = get_value_from_history(circle_radius_history[shape_position], use_instant_values)
            
            # 绘制圆形轮廓
            img_result.draw_circle(cx, cy, display_radius, color, thickness=2)
            
            # 绘制圆心
            img_result.draw_circle(cx, cy, 3, color, thickness=-1)
            
            # 显示半径信息
            radius_text = f"R:{display_radius}"
            img_result.draw_string(cx + 5, cy, radius_text, image.COLOR_RED)
            
            # 在控制台打印半径信息
            print(f"Circle Radius: {radius} pixels")

    # 过滤在圆形内部的四边形并处理保留的四边形
    if detected_circles:
        retained_quads = []
        removed_quad_count = 0
        filtered_quad_centers = set()  # 收集被过滤四边形的中心点
        filtered_quad_positions = []   # 收集需要清理的四边形位置信息

        for quad_info in quadrilaterals:
            quad_approx = quad_info['approx']
            quad_cx = quad_info['cx']
            quad_cy = quad_info['cy']
            quad_area = quad_info['area']
            is_max_rect = quad_info['is_max_rect']

            # 使用综合策略检查四边形是否在任何圆形内部
            is_inside_circle = False
            filter_reason = ""

            for circle_cx, circle_cy, circle_radius in detected_circles:
                inside, reason = is_quad_inside_circle_comprehensive(
                    quad_approx, circle_cx, circle_cy, circle_radius,
                    min_vertices_for_conservative_filter, min_vertices_for_aggressive_filter,
                    area_overlap_threshold
                )
                if inside:
                    is_inside_circle = True
                    filter_reason = reason
                    print(f"过滤掉在圆形内部的四边形: {reason}")
                    break

            # 如果不在任何圆形内部，处理这个四边形
            if not is_inside_circle:
                # 对保留的四边形进行完整处理
                process_retained_quadrilateral(
                    quad_approx, quad_cx, quad_cy, quad_area, is_max_rect, frame_count,
                    shape_tracking_data, kalman_filters, vertex_kalman_filters,
                    vertex_history, use_kalman, process_noise, measurement_noise,
                    vertex_history_size, edge_calc_method, position_tolerance,
                    last_frame_shapes, img_result, show_area, edge_history,
                    edge_history_size, use_instant_values
                )
                retained_quads.append(quad_approx)
            else:
                # 收集被过滤四边形的中心点信息（不进行任何处理）
                filtered_quad_centers.add((quad_cx, quad_cy))
                quad_position = ("Quad", quad_cx, quad_cy)
                filtered_quad_positions.append(quad_position)
                removed_quad_count += 1

        # 更新四边形列表和计数（只保留轮廓信息用于后续清理）
        quadrilaterals = retained_quads
        quadrilateral_count -= removed_quad_count
    else:
        # 没有检测到圆形，处理所有四边形
        retained_quads = []
        for quad_info in quadrilaterals:
            quad_approx = quad_info['approx']
            quad_cx = quad_info['cx']
            quad_cy = quad_info['cy']
            quad_area = quad_info['area']
            is_max_rect = quad_info['is_max_rect']

            # 对所有四边形进行完整处理
            process_retained_quadrilateral(
                quad_approx, quad_cx, quad_cy, quad_area, is_max_rect, frame_count,
                shape_tracking_data, kalman_filters, vertex_kalman_filters,
                vertex_history, use_kalman, process_noise, measurement_noise,
                vertex_history_size, edge_calc_method, position_tolerance,
                last_frame_shapes, img_result, show_area, edge_history,
                edge_history_size, use_instant_values
            )
            retained_quads.append(quad_approx)

        quadrilaterals = retained_quads
        filtered_quad_centers = set()
        filtered_quad_positions = []
        removed_quad_count = 0

    if removed_quad_count > 0:
        # 从detected_shapes中移除被过滤的四边形
        filtered_detected_shapes = []
        for detected_cx, detected_cy, detected_area, detected_vertices in detected_shapes:
            if detected_vertices == 4:  # 四边形
                # 检查这个四边形的中心点是否在被过滤的集合中
                if (detected_cx, detected_cy) not in filtered_quad_centers:
                    filtered_detected_shapes.append((detected_cx, detected_cy, detected_area, detected_vertices))
            else:
                # 非四边形，保留
                filtered_detected_shapes.append((detected_cx, detected_cy, detected_area, detected_vertices))

        detected_shapes = filtered_detected_shapes

    # 同时需要从last_frame_shapes中移除被过滤的四边形
    if "Quad" in last_frame_shapes and removed_quad_count > 0:
        filtered_quad_shapes = []
        for quad_cx, quad_cy, quad_area in last_frame_shapes["Quad"]:
            # 检查这个四边形的中心点是否在被过滤的集合中
            if (quad_cx, quad_cy) not in filtered_quad_centers:
                filtered_quad_shapes.append((quad_cx, quad_cy, quad_area))

        last_frame_shapes["Quad"] = filtered_quad_shapes

    # 清理被过滤四边形的所有跟踪数据
    if filtered_quad_positions:
        for quad_pos in filtered_quad_positions:
            try:
                # 清理形状跟踪数据
                if quad_pos in shape_tracking_data:
                    shape_tracking_data.pop(quad_pos, None)
                    print(f"清理被过滤四边形的跟踪数据: 位置 ({quad_pos[1]}, {quad_pos[2]})")

                # 清理卡尔曼滤波器
                if quad_pos in kalman_filters:
                    kalman_filters.pop(quad_pos, None)

                # 清理顶点卡尔曼滤波器
                if quad_pos in vertex_kalman_filters:
                    vertex_kalman_filters.pop(quad_pos, None)

                # 清理顶点历史记录
                if quad_pos in vertex_history:
                    vertex_history.pop(quad_pos, None)

                # 清理边长历史记录
                edge_keys_to_remove = []
                for key in edge_history.keys():
                    if isinstance(key, tuple) and len(key) >= 1 and key[0] == quad_pos:
                        edge_keys_to_remove.append(key)

                for key in edge_keys_to_remove:
                    edge_history.pop(key, None)

            except Exception as e:
                print(f"清理被过滤四边形数据时出错: {e}, 位置: {quad_pos}")

    if removed_quad_count > 0:
        print(f"总共过滤掉 {removed_quad_count} 个在圆形内部的四边形")

    # 显示统计信息
    info_text = f"Triangles: {triangle_count}, Quadrilaterals: {quadrilateral_count}, Circles: {circle_count}"
    img_result.draw_string(5, 5, info_text, image.COLOR_WHITE)
    
    # 使用卡尔曼滤波器预测此帧未检测到的形状
    if use_kalman:
        # 收集当前帧检测到的所有形状位置
        detected_positions = set()
        for shape_type, positions in last_frame_shapes.items():
            for pos in positions:
                cx, cy, _ = pos
                # 找到最近的已跟踪形状
                for tracked_pos in shape_tracking_data:
                    if tracked_pos[0] == shape_type:
                        distance = np.sqrt((cx - tracked_pos[1])**2 + (cy - tracked_pos[2])**2)
                        if distance < position_tolerance:
                            detected_positions.add(tracked_pos)
                            break
        
        # 对未检测到但正在跟踪的形状进行预测
        for pos in shape_tracking_data:
            if pos not in detected_positions:
                # 只处理在timeout范围内未检测到的形状
                if frame_count - shape_tracking_data[pos] <= shape_timeout:
                    shape_type = pos[0]
                    
                    # 设置颜色
                    if shape_type == "Triangle":
                        color = image.COLOR_RED
                    elif shape_type == "Quad":
                        color = image.COLOR_GREEN
                    else:  # Circle
                        color = image.COLOR_BLUE
                    
                    # 预测中心点位置
                    if pos in kalman_filters:
                        try:
                            predicted_pos = kalman_filters[pos].predict()
                            if predicted_pos:
                                pred_x, pred_y = predicted_pos
                                
                                # 显示预测文本
                                pred_text = f"Pred:{shape_type}"
                                img_result.draw_string(pred_x-20, pred_y, pred_text, color)
                                
                                # 如果是圆形，绘制预测的圆
                                if shape_type == "Circle" and pos in circle_radius_history:
                                    # 获取最近的半径历史记录
                                    recent_radiuses = circle_radius_history[pos]
                                    if recent_radiuses:
                                        # 使用最后记录的半径
                                        display_radius = recent_radiuses[-1]  # 最后记录的半径
                                        
                                        # 绘制预测位置的虚线圆
                                        for i in range(0, 360, CIRCLE_DASH_ANGLE_STEP):
                                            angle = i * np.pi / 180
                                            x = int(pred_x + display_radius * np.cos(angle))
                                            y = int(pred_y + display_radius * np.sin(angle))
                                            img_result.draw_circle(x, y, 2, color, thickness=-1)
                                
                                # 如果是三角形或四边形，使用顶点滤波器预测形状
                                elif pos in vertex_kalman_filters:
                                    try:
                                        predicted_vertices = vertex_kalman_filters[pos].predict()
                                        if predicted_vertices:
                                            # 绘制预测的形状边界（虚线）
                                            for i in range(len(predicted_vertices)):
                                                pt1 = predicted_vertices[i]
                                                pt2 = predicted_vertices[(i+1) % len(predicted_vertices)]
                                                
                                                # 画虚线连接顶点
                                                for j in range(DASH_LINE_SEGMENTS):
                                                    # 计算线段上的点
                                                    t = j / DASH_LINE_SEGMENTS
                                                    x1 = int(pt1[0] + (pt2[0] - pt1[0]) * t)
                                                    y1 = int(pt1[1] + (pt2[1] - pt1[1]) * t)
                                                    t2 = (j + 0.5) / DASH_LINE_SEGMENTS
                                                    x2 = int(pt1[0] + (pt2[0] - pt1[0]) * t2)
                                                    y2 = int(pt1[1] + (pt2[1] - pt1[1]) * t2)
                                                    
                                                    # 只画奇数段，形成虚线效果
                                                    if j % 2 == 0:
                                                        img_result.draw_line(x1, y1, x2, y2, color, 1)
                                                
                                                # 绘制顶点
                                                img_result.draw_circle(pt1[0], pt1[1], 2, color, thickness=-1)
                                                
                                                # 如果有边长历史记录，显示边长
                                                edge_key = (pos, i)
                                                if edge_key in edge_history and edge_history[edge_key]:
                                                    # 获取最后记录的边长
                                                    length_value = edge_history[edge_key][-1]
                                                    
                                                    # 计算边的中点
                                                    mid_x = (pt1[0] + pt2[0]) // 2
                                                    mid_y = (pt1[1] + pt2[1]) // 2
                                                    # 显示边长
                                                    img_result.draw_string(mid_x, mid_y, f"{length_value}", 
                                                                       image.COLOR_YELLOW)
                                    except Exception as e:
                                        print(f"顶点预测失败: {e}")
                                        # 如果预测失败，绘制简单的指示器
                                        radius = PREDICTION_INDICATOR_RADIUS
                                        for i in range(0, 360, SHAPE_DASH_ANGLE_STEP):  # 形成虚线圆
                                            angle = i * np.pi / 180
                                            x = int(pred_x + radius * np.cos(angle))
                                            y = int(pred_y + radius * np.sin(angle))
                                            img_result.draw_circle(x, y, 2, color, thickness=-1)
                                else:
                                    # 如果没有顶点滤波器，绘制简单的指示器
                                    radius = PREDICTION_INDICATOR_RADIUS
                                    for i in range(0, 360, SHAPE_DASH_ANGLE_STEP):  # 形成虚线圆
                                        angle = i * np.pi / 180
                                        x = int(pred_x + radius * np.cos(angle))
                                        y = int(pred_y + radius * np.sin(angle))
                                        img_result.draw_circle(x, y, 2, color, thickness=-1)
                                
                                # 更新该形状的最后预测帧
                                shape_tracking_data[pos] = frame_count
                        except Exception as e:
                            print(f"中心点预测失败: {e}")
    
    # 打印帧分隔符和统计信息
    print(f"\n---------- 帧 {frame_count} ----------")
    print(f"检测到 {triangle_count} 个三角形, {quadrilateral_count} 个四边形, {circle_count} 个圆形")
    
    # 显示最小检测到的面积
    if min_detected_area != float('inf'):
        min_area_text = f"Min Area: {int(min_detected_area)}"
    else:
        min_area_text = f"Min Area: None"
    img_result.draw_string(5, 65, min_area_text, image.COLOR_WHITE)
    
    # 显示当前最小面积阈值
    threshold_text = f"Min Area Threshold: {min_area}"
    img_result.draw_string(5, 80, threshold_text, image.COLOR_WHITE)
    
    # 显示闭运算状态
    closing_text = f"Closing: {'ON' if enable_closing else 'OFF'}, Kernel: {kernel_size}x{kernel_size}"
    img_result.draw_string(5, 95, closing_text, image.COLOR_WHITE)
    
    # 显示重复检测参数
    duplicate_text = f"Duplicate Threshold: Dist={duplicate_distance_threshold}, Ratio={duplicate_area_ratio:.1f}"
    img_result.draw_string(5, 125, duplicate_text, image.COLOR_WHITE)
    
    # 显示当前视图模式
    if show_debug:
        view_modes = ["Original", "Binary", "Edges", "After Closing"]
        view_text = view_modes[debug_view % len(view_modes)]
        img_result.draw_string(5, 35, f"View: {view_text}", image.COLOR_WHITE)
    
    # 显示过滤信息
    filter_text = f"Filtering: Shapes inside quads, quads inside circles (area:{area_overlap_threshold:.1f}, hybrid:{min_vertices_for_conservative_filter}/{min_vertices_for_aggressive_filter}) & no duplicates"
    img_result.draw_string(5, 110, filter_text, image.COLOR_WHITE)
    
    # 显示采样平均信息
    avg_text = f"Values: {'Instant' if use_instant_values else 'Average'}, History size={edge_history_size}"
    img_result.draw_string(5, 140, avg_text, image.COLOR_WHITE)
    
    # 显示卡尔曼滤波器状态
    kalman_text = f"Kalman Filter: {'ON' if use_kalman else 'OFF'}, P={process_noise}, M={measurement_noise}"
    img_result.draw_string(5, 155, kalman_text, image.COLOR_WHITE)
    
    # 显示边长计算方法
    edge_method_text = f"Edge Calc: {edge_calc_method.upper()}, Vertex History: {vertex_history_size}"
    img_result.draw_string(5, 170, edge_method_text, image.COLOR_WHITE)
    
    # 显示预处理状态
    if enable_preprocess:
        if frame_count < preprocess_start_frame:
            preprocess_text = f"预处理: 等待中 ({frame_count}/{preprocess_start_frame})"
            preprocess_color = image.COLOR_WHITE
        elif len(max_rectangles) < 2:
            preprocess_text = f"预处理: 正在识别最大矩形 ({len(max_rectangles)}/2)"
            preprocess_color = image.COLOR_YELLOW
        elif preprocess_stable_frames < preprocess_stable_threshold:
            preprocess_text = f"预处理: 等待矩形区域稳定 ({preprocess_stable_frames}/{preprocess_stable_threshold})"
            preprocess_color = image.COLOR_YELLOW
        else:
            preprocess_text = f"预处理: 已激活，仅处理矩形框内图形"
            preprocess_color = image.COLOR_GREEN
        img_result.draw_string(5, 185, preprocess_text, preprocess_color)
        
        # 显示最大矩形的面积信息
        if len(max_rectangles) > 0:
            for i, (_, area, _) in enumerate(max_rectangles):
                rect_text = f"主矩形{i+1}面积: {int(area)}"
                # 使用红色和蓝色代替洋红和青色
                rect_color = image.COLOR_RED if i == 0 else image.COLOR_BLUE
                img_result.draw_string(5, 200 + i*15, rect_text, rect_color)
    
    # 显示处理时间
    t_total = time.ticks_ms() - t_start
    timing_text = f"处理时间: {t_total}ms (转换:{t_convert}ms 二值:{t_binary}ms 闭运算:{t_closing}ms 边缘:{t_edge}ms 轮廓:{t_contour}ms)"
    img_result.draw_string(5, 20, timing_text, image.COLOR_WHITE)
    
    # 显示阈值和参数信息
    param_text = f"二值化阈值: {binary_threshold}, 轮廓近似因子: {epsilon_factor:.3f}"
    img_result.draw_string(5, 50, param_text, image.COLOR_WHITE)
    
    # 显示结果
    disp.show(img_result)
    
    # 在预处理完成后，突出显示两个最大矩形框
    if enable_preprocess and preprocess_started and len(max_rectangles) == 2 and preprocess_stable_frames >= preprocess_stable_threshold:
        # 在图像上绘制两个最大矩形的轮廓，使用明显的颜色
        for i, (_, _, approx) in enumerate(max_rectangles):
            # 转换为点列表
            points = [tuple(pt[0]) for pt in approx]
            # 使用红色和蓝色分别标记两个矩形
            highlight_color = image.COLOR_RED if i == 0 else image.COLOR_BLUE
            # 绘制粗线条轮廓
            for j in range(len(points)):
                pt1 = points[j]
                pt2 = points[(j+1) % len(points)]
                img_result.draw_line(pt1[0], pt1[1], pt2[0], pt2[1], highlight_color, 3)
            
            # 在矩形上标注其序号
            M = cv2.moments(approx)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                img_result.draw_string(cx-30, cy, f"主矩形 {i+1}", highlight_color)
        
        # 可视化掩码区域（半透明）
        show_mask_debug = False  # 控制是否显示掩码调试视图
        if show_mask_debug:
            mask = np.zeros_like(gray)
            for _, _, approx in max_rectangles:
                cv2.drawContours(mask, [approx], 0, 255, -1)
            
            # 转换掩码为彩色
            mask_colored = np.zeros_like(img_cv)
            mask_colored[mask > 0] = [0, 200, 0]  # 绿色
            
            # 叠加到原图
            overlay = cv2.addWeighted(img_cv, 1.0, mask_colored, 0.3, 0)
            img_overlay = image.cv2image(overlay, bgr=True, copy=False)
            
            # 显示叠加后的图像
            disp.show(img_overlay)
            time.sleep_ms(5)  # 短暂延时确保显示
        else:
            # 重新显示带有突出显示的图像
            disp.show(img_result)
    
    # 清理长时间未检测到的形状数据
    expired_positions = []
    for pos, last_seen_frame in shape_tracking_data.items():
        if frame_count - last_seen_frame > shape_timeout:
            expired_positions.append(pos)
    
    # 从跟踪数据中移除过期的形状位置
    for pos in expired_positions:
        try:
            if pos in shape_tracking_data:
                shape_type = pos[0]
                shape_tracking_data.pop(pos, None)
                print(f"清理过期形状: {shape_type} 在位置 ({pos[1]}, {pos[2]})")
                
            # 同时清理对应的卡尔曼滤波器
            if pos in kalman_filters:
                kalman_filters.pop(pos, None)
            if pos in vertex_kalman_filters:
                vertex_kalman_filters.pop(pos, None)
            if pos in vertex_history:
                vertex_history.pop(pos, None)
            
            # 清理对应的边长历史数据
            edge_keys_to_remove = []
            for edge_key in edge_history:
                if edge_key[0] == pos:
                    edge_keys_to_remove.append(edge_key)
            
            for key in edge_keys_to_remove:
                edge_history.pop(key, None)
            
            # 清理对应的圆半径历史数据
            if pos in circle_radius_history:
                circle_radius_history.pop(pos, None)
        except Exception as e:
            print(f"清理形状数据时出错: {e}, 位置: {pos}")
            
    if expired_positions:
        print(f"清理了 {len(expired_positions)} 个过期形状位置")
    
    # 重置当前帧形状列表，准备下一帧
    last_frame_shapes = {}
    
    # 增加总帧计数，用于预处理和形状跟踪
    frame_count += 1
    
    # 使用单独的计数器控制视图切换
    view_switch_count += 1
    if view_switch_count >= 10:  # 每10帧切换一次视图
        view_switch_count = 0
        if show_debug:
            # 确保视图模式始终在有效范围内
            debug_view = (debug_view + 1) % 4  # 现在有4种视图模式
            print(f"切换视图模式到: {['原图', '二值图', '边缘图', '闭运算图'][debug_view]}")
    
    # 简短延时，防止程序占用过多资源
    # 使用0毫秒延时让出CPU时间片给其他任务，但不会实际暂停程序
    time.sleep_ms(0)